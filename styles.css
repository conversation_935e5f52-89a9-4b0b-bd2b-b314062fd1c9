/* 全局样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Noto Serif SC', serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    background-attachment: fixed;
}

/* 导航栏样式 */
.navbar-brand {
    font-size: 1.5rem;
    letter-spacing: 1px;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 15px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

/* 妖怪卡片特殊样式 */
.yaoguai-card {
    cursor: pointer;
    height: 100%;
    overflow: hidden;
}

.yaoguai-card .card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.yaoguai-card:hover .card-img-top {
    transform: scale(1.05);
}

.yaoguai-card .card-body {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.yaoguai-card .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

/* 类别标签样式 */
.category-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    font-weight: 500;
}

.category-小妖 {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.category-头目 {
    background: linear-gradient(45deg, #27ae60, #229954);
    color: white;
}

.category-妖王 {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.category-人物 {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    color: white;
}

/* 统计标签样式 */
.category-stat {
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.category-stat:hover {
    transform: scale(1.05);
    opacity: 0.8;
}

/* 搜索框样式 */
.input-group-text {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #dee2e6;
}

.form-control, .form-select {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    background: rgba(255, 255, 255, 1);
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.98);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-body {
    padding: 2rem;
}

.modal-body .yaoguai-image {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.modal-body .yaoguai-description {
    line-height: 1.8;
    font-size: 1rem;
    color: #2c3e50;
    white-space: pre-line;
}

/* 分页样式 */
.pagination .page-link {
    border: none;
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    margin: 0 2px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

/* 返回顶部按钮 */
.btn-floating {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: none;
    z-index: 1000;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.btn-floating:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* 加载动画 */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .yaoguai-card .card-img-top {
        height: 150px;
    }
    
    .modal-dialog {
        margin: 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .btn-floating {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .category-stat {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
        margin-bottom: 0.5rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
