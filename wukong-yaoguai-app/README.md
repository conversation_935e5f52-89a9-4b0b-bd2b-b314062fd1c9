# 黑神话悟空妖怪平生录 - 全栈应用

基于 Koa.js + Vue3 + Element Plus 的现代化全栈应用，用于展示《黑神话悟空》游戏中的妖怪信息。

## 🎮 项目简介

本项目是《黑神话悟空妖怪平生录》的全栈实现版本，包含：
- **后端**：Koa.js RESTful API 服务
- **前端**：Vue3 + Element Plus 响应式网页应用
- **数据**：203 个妖怪的完整信息，分为 4 个类别

## 🏗️ 项目架构

```
wukong-yaoguai-app/
├── backend/                 # Koa.js 后端服务
│   ├── app.js              # 主应用文件
│   ├── yaoguai-data.json   # 妖怪数据文件
│   └── package.json        # 后端依赖配置
├── frontend/               # Vue3 前端应用
│   ├── src/
│   │   ├── api/           # API 接口封装
│   │   ├── stores/        # Pinia 状态管理
│   │   ├── views/         # 页面组件
│   │   ├── router/        # 路由配置
│   │   └── main.ts        # 应用入口
│   └── package.json       # 前端依赖配置
└── README.md              # 项目文档
```

## 🛠️ 技术栈

### 后端技术
- **Node.js** 22.16.0
- **Koa.js** - 轻量级 Web 框架
- **@koa/router** - 路由中间件
- **@koa/cors** - 跨域处理
- **koa-bodyparser** - 请求体解析
- **nodemon** - 开发热重载

### 前端技术
- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全
- **Element Plus** - Vue 3 组件库
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **Axios** - HTTP 客户端
- **Vite** - 构建工具

## 🚀 快速开始

### 环境要求
- Node.js 18+ (推荐使用 nvm 管理版本)
- npm 或 yarn

### 1. 克隆项目
```bash
git clone <repository-url>
cd wukong-yaoguai-app
```

### 2. 启动后端服务
```bash
cd backend
npm install
npm run dev
```
后端服务将在 `http://localhost:3000` 启动

### 3. 启动前端服务
```bash
cd frontend
npm install
npm run dev
```
前端应用将在 `http://localhost:5173` 启动

### 4. 访问应用
打开浏览器访问 `http://localhost:5173`

## 📡 API 接口

### 基础信息
- **Base URL**: `http://localhost:3000/api`
- **数据格式**: JSON
- **编码**: UTF-8

### 接口列表

#### 1. 获取妖怪列表
```
GET /api/yaoguai
```

**查询参数**：
- `page` (number): 页码，默认 1
- `pageSize` (number): 每页数量，默认 12
- `search` (string): 搜索关键词
- `category` (string): 类别筛选
- `sortBy` (string): 排序字段，默认 'id'
- `sortOrder` (string): 排序方向，'asc' | 'desc'

**响应示例**：
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": "1.1",
        "name": "狼斥候",
        "category": "小妖",
        "description": "妖怪描述...",
        "image": "图片路径"
      }
    ],
    "pagination": {
      "current": 1,
      "pageSize": 12,
      "total": 203,
      "totalPages": 17
    }
  }
}
```

#### 2. 获取妖怪详情
```
GET /api/yaoguai/:id
```

#### 3. 获取统计信息
```
GET /api/statistics
```

#### 4. 搜索建议
```
GET /api/search/suggestions?q=关键词
```

#### 5. 健康检查
```
GET /api/health
```

## ✨ 功能特性

### 🔍 搜索和筛选
- **实时搜索**：支持妖怪名称和描述搜索
- **智能建议**：输入时显示搜索建议
- **类别筛选**：按妖怪类别筛选
- **组合查询**：搜索和筛选可同时使用

### 📄 分页和排序
- **智能分页**：支持页码跳转和页面大小调整
- **多字段排序**：支持按 ID、名称等字段排序
- **状态保持**：筛选条件在分页时保持

### 🎯 详情展示
- **详情页面**：独立的妖怪详情页
- **图片展示**：高质量图片显示
- **格式化文本**：优化的描述文本排版
- **导航功能**：便捷的页面导航

### 📊 统计分析
- **数据统计**：各类别妖怪数量统计
- **可视化图表**：直观的数据展示
- **详细信息**：完整的数据分析

### 📱 响应式设计
- **移动优先**：优化的移动端体验
- **自适应布局**：支持各种屏幕尺寸
- **触摸友好**：移动设备交互优化

## 🎨 界面设计

### 设计风格
- **现代化**：简洁的 Material Design 风格
- **渐变背景**：美观的视觉效果
- **动画效果**：流畅的交互动画
- **中文优化**：针对中文内容优化的字体和排版

### 主题色彩
- **主色调**：渐变蓝紫色 (#667eea → #764ba2)
- **小妖**：蓝色系 (#409EFF)
- **头目**：绿色系 (#67C23A)
- **妖王**：橙色系 (#E6A23C)
- **人物**：紫色系 (#9C27B0)

## 🔧 开发指南

### 后端开发
```bash
cd backend
npm run dev    # 开发模式
npm start      # 生产模式
```

### 前端开发
```bash
cd frontend
npm run dev    # 开发服务器
npm run build  # 构建生产版本
npm run preview # 预览构建结果
```

### 代码规范
- 使用 ESLint 进行代码检查
- 使用 TypeScript 确保类型安全
- 遵循 Vue 3 Composition API 最佳实践

## 📊 项目统计

- **妖怪总数**：203 个
- **类别数量**：4 种（小妖、头目、妖王、人物）
- **API 接口**：5 个
- **前端页面**：3 个
- **开发时间**：约 4 小时（AI 协助）

## 🐛 故障排除

### 常见问题

1. **后端启动失败**
   - 检查 Node.js 版本是否为 18+
   - 确保端口 3000 未被占用
   - 检查 `yaoguai-data.json` 文件是否存在

2. **前端无法连接后端**
   - 确保后端服务正在运行
   - 检查 API 基础 URL 配置
   - 查看浏览器控制台错误信息

3. **图片显示问题**
   - 图片路径相对于后端服务
   - 检查图片文件是否存在
   - 确保静态文件服务正常

### 调试技巧
- 使用浏览器开发者工具查看网络请求
- 查看后端控制台日志
- 使用 Vue DevTools 调试前端状态

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📜 许可证

本项目仅用于学习和展示目的。游戏相关内容版权归《黑神话悟空》开发商所有。

## 🙏 致谢

- 感谢《黑神话悟空》开发团队提供的精彩游戏内容
- 感谢开源社区提供的优秀技术框架
- 感谢 Augment Agent 提供的 AI 开发协助
