{"name": "wukong-yaoguai-backend", "version": "1.0.0", "description": "黑神话悟空妖怪平生录后端API服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["koa", "api", "wukong", "yaoguai"], "author": "", "license": "ISC", "dependencies": {"@koa/cors": "^5.0.0", "@koa/router": "^13.1.0", "koa": "^3.0.0", "koa-bodyparser": "^4.4.1", "koa-static": "^5.0.0", "nodemon": "^3.1.10"}}