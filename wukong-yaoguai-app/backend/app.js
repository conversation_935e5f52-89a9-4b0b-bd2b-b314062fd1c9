const Koa = require('koa');
const Router = require('@koa/router');
const cors = require('@koa/cors');
const bodyParser = require('koa-bodyparser');
const serve = require('koa-static');
const fs = require('fs').promises;
const path = require('path');

const app = new Koa();
const router = new Router();

// 中间件
app.use(cors({
    origin: '*', // 开发环境允许所有来源，生产环境应该限制
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowHeaders: ['Content-Type', 'Authorization']
}));

app.use(bodyParser());

// 静态文件服务（用于图片等资源）
app.use(serve(path.join(__dirname, 'public')));

// 加载妖怪数据
let yaoguaiData = null;

async function loadYaoguaiData() {
    try {
        const data = await fs.readFile(path.join(__dirname, 'yaoguai-data.json'), 'utf8');
        yaoguaiData = JSON.parse(data);
        console.log(`✅ 成功加载 ${yaoguaiData.totalCount} 个妖怪数据`);
    } catch (error) {
        console.error('❌ 加载妖怪数据失败:', error);
        process.exit(1);
    }
}

// API 路由

// 获取所有妖怪列表（支持分页、搜索、筛选）
router.get('/api/yaoguai', async (ctx) => {
    try {
        const {
            page = 1,
            pageSize = 12,
            search = '',
            category = '',
            sortBy = 'id',
            sortOrder = 'asc'
        } = ctx.query;

        let filteredYaoguai = [...yaoguaiData.yaoguai];

        // 搜索过滤
        if (search) {
            const searchTerm = search.toLowerCase();
            filteredYaoguai = filteredYaoguai.filter(yaoguai =>
                yaoguai.name.toLowerCase().includes(searchTerm) ||
                yaoguai.description.toLowerCase().includes(searchTerm)
            );
        }

        // 类别过滤
        if (category) {
            filteredYaoguai = filteredYaoguai.filter(yaoguai =>
                yaoguai.category === category
            );
        }

        // 排序
        filteredYaoguai.sort((a, b) => {
            let aValue = a[sortBy];
            let bValue = b[sortBy];

            // 处理 id 字段的数字排序
            if (sortBy === 'id') {
                aValue = parseFloat(aValue);
                bValue = parseFloat(bValue);
            }

            if (sortOrder === 'desc') {
                return bValue > aValue ? 1 : -1;
            }
            return aValue > bValue ? 1 : -1;
        });

        // 分页
        const total = filteredYaoguai.length;
        const startIndex = (parseInt(page) - 1) * parseInt(pageSize);
        const endIndex = startIndex + parseInt(pageSize);
        const paginatedYaoguai = filteredYaoguai.slice(startIndex, endIndex);

        ctx.body = {
            success: true,
            data: {
                list: paginatedYaoguai,
                pagination: {
                    current: parseInt(page),
                    pageSize: parseInt(pageSize),
                    total,
                    totalPages: Math.ceil(total / parseInt(pageSize))
                }
            }
        };
    } catch (error) {
        console.error('获取妖怪列表失败:', error);
        ctx.status = 500;
        ctx.body = {
            success: false,
            message: '服务器内部错误'
        };
    }
});

// 根据ID获取单个妖怪详情
router.get('/api/yaoguai/:id', async (ctx) => {
    try {
        const { id } = ctx.params;
        const yaoguai = yaoguaiData.yaoguai.find(y => y.id === id);

        if (!yaoguai) {
            ctx.status = 404;
            ctx.body = {
                success: false,
                message: '妖怪不存在'
            };
            return;
        }

        ctx.body = {
            success: true,
            data: yaoguai
        };
    } catch (error) {
        console.error('获取妖怪详情失败:', error);
        ctx.status = 500;
        ctx.body = {
            success: false,
            message: '服务器内部错误'
        };
    }
});

// 获取统计信息
router.get('/api/statistics', async (ctx) => {
    try {
        const stats = {
            totalCount: yaoguaiData.totalCount,
            categories: yaoguaiData.categories,
            categoryStats: {}
        };

        // 统计各类别数量
        yaoguaiData.categories.forEach(category => {
            stats.categoryStats[category] = yaoguaiData.yaoguai.filter(
                y => y.category === category
            ).length;
        });

        ctx.body = {
            success: true,
            data: stats
        };
    } catch (error) {
        console.error('获取统计信息失败:', error);
        ctx.status = 500;
        ctx.body = {
            success: false,
            message: '服务器内部错误'
        };
    }
});

// 搜索建议（自动完成）
router.get('/api/search/suggestions', async (ctx) => {
    try {
        const { q = '' } = ctx.query;
        
        if (!q || q.length < 2) {
            ctx.body = {
                success: true,
                data: []
            };
            return;
        }

        const searchTerm = q.toLowerCase();
        const suggestions = yaoguaiData.yaoguai
            .filter(yaoguai => yaoguai.name.toLowerCase().includes(searchTerm))
            .slice(0, 10)
            .map(yaoguai => ({
                id: yaoguai.id,
                name: yaoguai.name,
                category: yaoguai.category
            }));

        ctx.body = {
            success: true,
            data: suggestions
        };
    } catch (error) {
        console.error('获取搜索建议失败:', error);
        ctx.status = 500;
        ctx.body = {
            success: false,
            message: '服务器内部错误'
        };
    }
});

// 健康检查
router.get('/api/health', async (ctx) => {
    ctx.body = {
        success: true,
        message: '服务运行正常',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    };
});

// 错误处理中间件
app.use(async (ctx, next) => {
    try {
        await next();
    } catch (err) {
        console.error('请求处理错误:', err);
        ctx.status = err.status || 500;
        ctx.body = {
            success: false,
            message: err.message || '服务器内部错误'
        };
    }
});

// 注册路由
app.use(router.routes());
app.use(router.allowedMethods());

// 启动服务器
const PORT = process.env.PORT || 3000;

async function startServer() {
    await loadYaoguaiData();
    
    app.listen(PORT, () => {
        console.log(`🚀 Koa.js 服务器启动成功！`);
        console.log(`📡 服务地址: http://localhost:${PORT}`);
        console.log(`📚 API 文档:`);
        console.log(`   GET  /api/yaoguai          - 获取妖怪列表`);
        console.log(`   GET  /api/yaoguai/:id      - 获取妖怪详情`);
        console.log(`   GET  /api/statistics       - 获取统计信息`);
        console.log(`   GET  /api/search/suggestions - 搜索建议`);
        console.log(`   GET  /api/health           - 健康检查`);
    });
}

startServer().catch(console.error);
