import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        title: '黑神话悟空妖怪平生录'
      }
    },
    {
      path: '/yaoguai/:id',
      name: 'yaoguai-detail',
      component: () => import('../views/YaoguaiDetailView.vue'),
      meta: {
        title: '妖怪详情'
      }
    },
    {
      path: '/statistics',
      name: 'statistics',
      component: () => import('../views/StatisticsView.vue'),
      meta: {
        title: '统计信息'
      }
    }
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title as string
  }
  next()
})

export default router
