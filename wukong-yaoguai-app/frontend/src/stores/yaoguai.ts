import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { yaoguaiApi, type Yaoguai, type YaoguaiListParams } from '@/api'
import { ElMessage } from 'element-plus'

export const useYaoguaiStore = defineStore('yaoguai', () => {
  // 状态
  const yaoguaiList = ref<Yaoguai[]>([])
  const currentYaoguai = ref<Yaoguai | null>(null)
  const loading = ref(false)
  const pagination = ref({
    current: 1,
    pageSize: 12,
    total: 0,
    totalPages: 0
  })
  const statistics = ref({
    totalCount: 0,
    categories: [] as string[],
    categoryStats: {} as Record<string, number>
  })

  // 搜索和筛选参数
  const searchParams = ref<YaoguaiListParams>({
    page: 1,
    pageSize: 12,
    search: '',
    category: '',
    sortBy: 'id',
    sortOrder: 'asc'
  })

  // 计算属性
  const hasData = computed(() => yaoguaiList.value.length > 0)
  const isEmpty = computed(() => !loading.value && yaoguaiList.value.length === 0)

  // 获取妖怪列表
  const fetchYaoguaiList = async (params?: Partial<YaoguaiListParams>) => {
    try {
      loading.value = true
      
      // 合并参数
      const finalParams = { ...searchParams.value, ...params }
      searchParams.value = finalParams

      const response = await yaoguaiApi.getYaoguaiList(finalParams)
      
      if (response.success) {
        yaoguaiList.value = response.data.list
        pagination.value = response.data.pagination
      } else {
        ElMessage.error('获取妖怪列表失败')
      }
    } catch (error) {
      console.error('获取妖怪列表失败:', error)
      ElMessage.error('网络错误，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 获取妖怪详情
  const fetchYaoguaiDetail = async (id: string) => {
    try {
      loading.value = true
      const response = await yaoguaiApi.getYaoguaiDetail(id)
      
      if (response.success) {
        currentYaoguai.value = response.data
        return response.data
      } else {
        ElMessage.error('获取妖怪详情失败')
        return null
      }
    } catch (error) {
      console.error('获取妖怪详情失败:', error)
      ElMessage.error('网络错误，请稍后重试')
      return null
    } finally {
      loading.value = false
    }
  }

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const response = await yaoguaiApi.getStatistics()
      
      if (response.success) {
        statistics.value = response.data
      } else {
        ElMessage.error('获取统计信息失败')
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
      ElMessage.error('网络错误，请稍后重试')
    }
  }

  // 搜索妖怪
  const searchYaoguai = async (keyword: string) => {
    searchParams.value.search = keyword
    searchParams.value.page = 1
    await fetchYaoguaiList()
  }

  // 按类别筛选
  const filterByCategory = async (category: string) => {
    searchParams.value.category = category
    searchParams.value.page = 1
    await fetchYaoguaiList()
  }

  // 清除筛选
  const clearFilters = async () => {
    searchParams.value = {
      page: 1,
      pageSize: 12,
      search: '',
      category: '',
      sortBy: 'id',
      sortOrder: 'asc'
    }
    await fetchYaoguaiList()
  }

  // 分页
  const changePage = async (page: number) => {
    searchParams.value.page = page
    await fetchYaoguaiList()
  }

  // 改变每页大小
  const changePageSize = async (pageSize: number) => {
    searchParams.value.pageSize = pageSize
    searchParams.value.page = 1
    await fetchYaoguaiList()
  }

  // 排序
  const sortYaoguai = async (sortBy: string, sortOrder: 'asc' | 'desc') => {
    searchParams.value.sortBy = sortBy
    searchParams.value.sortOrder = sortOrder
    searchParams.value.page = 1
    await fetchYaoguaiList()
  }

  // 重置当前妖怪
  const resetCurrentYaoguai = () => {
    currentYaoguai.value = null
  }

  return {
    // 状态
    yaoguaiList,
    currentYaoguai,
    loading,
    pagination,
    statistics,
    searchParams,
    
    // 计算属性
    hasData,
    isEmpty,
    
    // 方法
    fetchYaoguaiList,
    fetchYaoguaiDetail,
    fetchStatistics,
    searchYaoguai,
    filterByCategory,
    clearFilters,
    changePage,
    changePageSize,
    sortYaoguai,
    resetCurrentYaoguai
  }
})
