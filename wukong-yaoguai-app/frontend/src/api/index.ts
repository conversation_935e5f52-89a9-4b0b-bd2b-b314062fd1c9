import axios from 'axios'

// 创建 axios 实例
const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加 token 等认证信息
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

// 妖怪相关接口
export interface Yaoguai {
  id: string
  name: string
  category: string
  description: string
  image: string
}

export interface YaoguaiListParams {
  page?: number
  pageSize?: number
  search?: string
  category?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface YaoguaiListResponse {
  success: boolean
  data: {
    list: Yaoguai[]
    pagination: {
      current: number
      pageSize: number
      total: number
      totalPages: number
    }
  }
}

export interface YaoguaiDetailResponse {
  success: boolean
  data: Yaoguai
}

export interface StatisticsResponse {
  success: boolean
  data: {
    totalCount: number
    categories: string[]
    categoryStats: Record<string, number>
  }
}

export interface SearchSuggestion {
  id: string
  name: string
  category: string
}

export interface SearchSuggestionsResponse {
  success: boolean
  data: SearchSuggestion[]
}

// API 方法
export const yaoguaiApi = {
  // 获取妖怪列表
  getYaoguaiList(params: YaoguaiListParams = {}): Promise<YaoguaiListResponse> {
    return api.get('/yaoguai', { params })
  },

  // 获取妖怪详情
  getYaoguaiDetail(id: string): Promise<YaoguaiDetailResponse> {
    return api.get(`/yaoguai/${id}`)
  },

  // 获取统计信息
  getStatistics(): Promise<StatisticsResponse> {
    return api.get('/statistics')
  },

  // 获取搜索建议
  getSearchSuggestions(q: string): Promise<SearchSuggestionsResponse> {
    return api.get('/search/suggestions', { params: { q } })
  },

  // 健康检查
  healthCheck(): Promise<any> {
    return api.get('/health')
  }
}

export default api
