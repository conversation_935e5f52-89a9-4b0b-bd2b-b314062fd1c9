<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useYaoguaiStore } from '@/stores/yaoguai'

const router = useRouter()
const yaoguaiStore = useYaoguaiStore()

// 生命周期
onMounted(async () => {
  await yaoguaiStore.fetchStatistics()
})

// 计算属性
const chartData = computed(() => {
  const data = []
  for (const [category, count] of Object.entries(yaoguaiStore.statistics.categoryStats)) {
    data.push({
      name: category,
      value: count,
      color: getCategoryColor(category)
    })
  }
  return data
})

// 方法
const goBack = () => {
  router.back()
}

const goHome = () => {
  router.push('/')
}

const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    '小妖': '#409EFF',
    '头目': '#67C23A',
    '妖王': '#E6A23C',
    '人物': '#9C27B0'
  }
  return colors[category] || '#909399'
}

const getPercentage = (count: number) => {
  if (yaoguaiStore.statistics.totalCount === 0) return 0
  return Math.round((count / yaoguaiStore.statistics.totalCount) * 100)
}
</script>

<template>
  <div class="statistics-container">
    <div class="statistics-content">
      <!-- 头部导航 -->
      <div class="header-nav">
        <el-button @click="goBack" type="primary" plain>
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-button @click="goHome" plain>
          <el-icon><House /></el-icon>
          首页
        </el-button>
      </div>

      <!-- 统计信息卡片 -->
      <el-card class="statistics-card">
        <template #header>
          <div class="card-header">
            <h1 class="title">
              <el-icon><DataAnalysis /></el-icon>
              妖怪统计信息
            </h1>
          </div>
        </template>

        <!-- 总体统计 -->
        <div class="overview-section">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="6">
              <el-card class="stat-card total-card">
                <div class="stat-content">
                  <el-icon class="stat-icon"><Collection /></el-icon>
                  <div class="stat-info">
                    <div class="stat-number">{{ yaoguaiStore.statistics.totalCount }}</div>
                    <div class="stat-label">妖怪总数</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            
            <el-col :xs="24" :sm="12" :md="6">
              <el-card class="stat-card category-card">
                <div class="stat-content">
                  <el-icon class="stat-icon"><Grid /></el-icon>
                  <div class="stat-info">
                    <div class="stat-number">{{ yaoguaiStore.statistics.categories.length }}</div>
                    <div class="stat-label">妖怪类别</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            
            <el-col :xs="24" :sm="12" :md="6">
              <el-card class="stat-card poem-card">
                <div class="stat-content">
                  <el-icon class="stat-icon"><Document /></el-icon>
                  <div class="stat-info">
                    <div class="stat-number">{{ yaoguaiStore.statistics.totalCount }}</div>
                    <div class="stat-label">小诗数量</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            
            <el-col :xs="24" :sm="12" :md="6">
              <el-card class="stat-card story-card">
                <div class="stat-content">
                  <el-icon class="stat-icon"><Reading /></el-icon>
                  <div class="stat-info">
                    <div class="stat-number">{{ yaoguaiStore.statistics.totalCount }}</div>
                    <div class="stat-label">妖生故事</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <el-divider />

        <!-- 分类统计 -->
        <div class="category-section">
          <h2 class="section-title">
            <el-icon><PieChart /></el-icon>
            分类统计
          </h2>
          
          <el-row :gutter="20">
            <el-col :xs="24" :lg="12">
              <div class="category-list">
                <div
                  v-for="(count, category) in yaoguaiStore.statistics.categoryStats"
                  :key="category"
                  class="category-item"
                >
                  <div class="category-info">
                    <el-tag
                      :color="getCategoryColor(category)"
                      effect="dark"
                      size="large"
                      class="category-tag"
                    >
                      {{ category }}
                    </el-tag>
                    <div class="category-stats">
                      <span class="count">{{ count }}</span>
                      <span class="percentage">{{ getPercentage(count) }}%</span>
                    </div>
                  </div>
                  <el-progress
                    :percentage="getPercentage(count)"
                    :color="getCategoryColor(category)"
                    :stroke-width="8"
                    class="category-progress"
                  />
                </div>
              </div>
            </el-col>
            
            <el-col :xs="24" :lg="12">
              <div class="chart-container">
                <div class="pie-chart">
                  <div
                    v-for="item in chartData"
                    :key="item.name"
                    class="pie-slice"
                    :style="{
                      '--slice-color': item.color,
                      '--slice-percentage': getPercentage(item.value) + '%'
                    }"
                  >
                    <div class="slice-label">
                      {{ item.name }}<br>
                      {{ item.value }}
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <el-divider />

        <!-- 详细信息 -->
        <div class="details-section">
          <h2 class="section-title">
            <el-icon><InfoFilled /></el-icon>
            详细信息
          </h2>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="数据来源">黑神话悟空妖怪平生录</el-descriptions-item>
            <el-descriptions-item label="数据格式">JSON</el-descriptions-item>
            <el-descriptions-item label="最大类别">
              {{ Object.entries(yaoguaiStore.statistics.categoryStats).reduce((a, b) => a[1] > b[1] ? a : b)[0] }}
            </el-descriptions-item>
            <el-descriptions-item label="最小类别">
              {{ Object.entries(yaoguaiStore.statistics.categoryStats).reduce((a, b) => a[1] < b[1] ? a : b)[0] }}
            </el-descriptions-item>
            <el-descriptions-item label="平均每类">
              {{ Math.round(yaoguaiStore.statistics.totalCount / yaoguaiStore.statistics.categories.length) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ new Date().toLocaleDateString() }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.statistics-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.statistics-content {
  max-width: 1200px;
  margin: 0 auto;
}

.header-nav {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.statistics-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 15px;
}

.card-header {
  text-align: center;
}

.title {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.overview-section {
  margin-bottom: 30px;
}

.stat-card {
  border: none;
  border-radius: 12px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.total-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.category-card {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.poem-card {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.story-card {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

.section-title {
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-list {
  padding: 20px;
}

.category-item {
  margin-bottom: 25px;
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.category-tag {
  font-size: 1rem;
  padding: 8px 16px;
}

.category-stats {
  display: flex;
  gap: 15px;
  align-items: center;
}

.count {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
}

.percentage {
  font-size: 1rem;
  color: #7f8c8d;
}

.category-progress {
  margin-top: 5px;
}

.chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.pie-chart {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: conic-gradient(
    #409EFF 0% 44%,
    #67C23A 44% 71%,
    #E6A23C 71% 84%,
    #9C27B0 84% 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.pie-chart::after {
  content: '';
  width: 100px;
  height: 100px;
  background: white;
  border-radius: 50%;
  position: absolute;
}

.details-section {
  margin-top: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-container {
    padding: 10px;
  }

  .title {
    font-size: 1.5rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .chart-container {
    height: 300px;
  }

  .pie-chart {
    width: 150px;
    height: 150px;
  }

  .pie-chart::after {
    width: 75px;
    height: 75px;
  }
}
</style>
