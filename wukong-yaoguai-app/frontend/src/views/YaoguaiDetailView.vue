<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useYaoguaiStore } from '@/stores/yaoguai'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const yaoguaiStore = useYaoguaiStore()

const loading = ref(false)

// 计算属性
const yaoguaiId = computed(() => route.params.id as string)

// 生命周期
onMounted(async () => {
  await loadYaoguaiDetail()
})

// 方法
const loadYaoguaiDetail = async () => {
  if (!yaoguaiId.value) {
    ElMessage.error('妖怪ID不存在')
    router.push('/')
    return
  }

  loading.value = true
  try {
    await yaoguaiStore.fetchYaoguaiDetail(yaoguaiId.value)
    if (!yaoguaiStore.currentYaoguai) {
      ElMessage.error('妖怪不存在')
      router.push('/')
    }
  } catch (error) {
    console.error('加载妖怪详情失败:', error)
    ElMessage.error('加载失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.back()
}

const goHome = () => {
  router.push('/')
}

const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    '小妖': '#409EFF',
    '头目': '#67C23A',
    '妖王': '#E6A23C',
    '人物': '#9C27B0'
  }
  return colors[category] || '#909399'
}

const formatDescription = (description: string) => {
  // 将描述文本按段落分割并格式化
  return description.split('\n\n').filter(p => p.trim())
}
</script>

<template>
  <div class="detail-container" v-loading="loading">
    <div class="detail-content" v-if="yaoguaiStore.currentYaoguai">
      <!-- 头部导航 -->
      <div class="header-nav">
        <el-button @click="goBack" type="primary" plain>
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-button @click="goHome" plain>
          <el-icon><House /></el-icon>
          首页
        </el-button>
      </div>

      <!-- 妖怪信息卡片 -->
      <el-card class="yaoguai-detail-card">
        <div class="detail-header">
          <div class="image-section">
            <el-image
              :src="yaoguaiStore.currentYaoguai.image"
              :alt="yaoguaiStore.currentYaoguai.name"
              fit="cover"
              class="detail-image"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <span>图片加载失败</span>
                </div>
              </template>
            </el-image>
          </div>
          
          <div class="info-section">
            <h1 class="yaoguai-title">{{ yaoguaiStore.currentYaoguai.name }}</h1>
            <div class="yaoguai-meta">
              <el-tag
                :color="getCategoryColor(yaoguaiStore.currentYaoguai.category)"
                effect="dark"
                size="large"
              >
                {{ yaoguaiStore.currentYaoguai.category }}
              </el-tag>
              <span class="yaoguai-id">编号: {{ yaoguaiStore.currentYaoguai.id }}</span>
            </div>
          </div>
        </div>

        <el-divider />

        <!-- 妖怪描述 -->
        <div class="description-section">
          <h2 class="section-title">
            <el-icon><Document /></el-icon>
            妖怪平生
          </h2>
          <div class="description-content">
            <div
              v-for="(paragraph, index) in formatDescription(yaoguaiStore.currentYaoguai.description)"
              :key="index"
              class="paragraph"
              v-html="paragraph"
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 加载状态 -->
    <div v-else-if="loading" class="loading-container">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <p>正在加载妖怪详情...</p>
    </div>

    <!-- 错误状态 -->
    <el-empty v-else description="妖怪不存在或加载失败" :image-size="200">
      <el-button type="primary" @click="goHome">
        返回首页
      </el-button>
    </el-empty>
  </div>
</template>

<style scoped>
.detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.detail-content {
  max-width: 1000px;
  margin: 0 auto;
}

.header-nav {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.yaoguai-detail-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 15px;
}

.detail-header {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

.image-section {
  flex-shrink: 0;
}

.detail-image {
  width: 300px;
  height: 400px;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  background: #f5f7fa;
  border-radius: 12px;
}

.info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.yaoguai-title {
  font-size: 3rem;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 20px 0;
  text-align: center;
}

.yaoguai-meta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.yaoguai-id {
  font-size: 1.1rem;
  color: #7f8c8d;
  font-weight: 500;
}

.description-section {
  margin-top: 20px;
}

.section-title {
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.description-content {
  line-height: 1.8;
  font-size: 1.1rem;
  color: #2c3e50;
}

.paragraph {
  margin-bottom: 1.5rem;
  text-align: justify;
  text-indent: 2em;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  color: white;
}

.loading-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-container {
    padding: 10px;
  }

  .detail-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .detail-image {
    width: 250px;
    height: 300px;
  }

  .yaoguai-title {
    font-size: 2rem;
  }

  .yaoguai-meta {
    flex-direction: column;
    gap: 10px;
  }

  .description-content {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .detail-image {
    width: 200px;
    height: 250px;
  }

  .yaoguai-title {
    font-size: 1.5rem;
  }
}
</style>
