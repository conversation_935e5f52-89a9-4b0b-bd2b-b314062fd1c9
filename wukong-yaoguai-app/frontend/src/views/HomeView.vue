<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useYaoguaiStore } from '@/stores/yaoguai'
import { yaoguaiApi, type SearchSuggestion } from '@/api'
import { ElMessage } from 'element-plus'

const router = useRouter()
const yaoguaiStore = useYaoguaiStore()

// 响应式数据
const searchKeyword = ref('')
const selectedCategory = ref('')
const searchSuggestions = ref<SearchSuggestion[]>([])
const showSuggestions = ref(false)

// 计算属性
const categoryOptions = computed(() => [
  { label: '全部类别', value: '' },
  ...yaoguaiStore.statistics.categories.map(cat => ({
    label: cat,
    value: cat
  }))
])

// 生命周期
onMounted(async () => {
  await yaoguaiStore.fetchStatistics()
  await yaoguaiStore.fetchYaoguaiList()
})

// 方法
const handleSearch = async () => {
  if (searchKeyword.value.trim()) {
    await yaoguaiStore.searchYaoguai(searchKeyword.value.trim())
  } else {
    await yaoguaiStore.clearFilters()
  }
  showSuggestions.value = false
}

const handleCategoryChange = async () => {
  await yaoguaiStore.filterByCategory(selectedCategory.value)
}

const handleClearFilters = async () => {
  searchKeyword.value = ''
  selectedCategory.value = ''
  await yaoguaiStore.clearFilters()
}

const handlePageChange = async (page: number) => {
  await yaoguaiStore.changePage(page)
}

const handlePageSizeChange = async (pageSize: number) => {
  await yaoguaiStore.changePageSize(pageSize)
}

const viewYaoguaiDetail = (id: string) => {
  router.push(`/yaoguai/${id}`)
}

// 搜索建议
const handleSearchInput = async () => {
  if (searchKeyword.value.length >= 2) {
    try {
      const response = await yaoguaiApi.getSearchSuggestions(searchKeyword.value)
      if (response.success) {
        searchSuggestions.value = response.data
        showSuggestions.value = true
      }
    } catch (error) {
      console.error('获取搜索建议失败:', error)
    }
  } else {
    showSuggestions.value = false
  }
}

const selectSuggestion = (suggestion: SearchSuggestion) => {
  searchKeyword.value = suggestion.name
  showSuggestions.value = false
  handleSearch()
}

const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    '小妖': '#409EFF',
    '头目': '#67C23A',
    '妖王': '#E6A23C',
    '人物': '#9C27B0'
  }
  return colors[category] || '#909399'
}
</script>

<template>
  <div class="home-container">
    <!-- 头部搜索区域 -->
    <div class="search-section">
      <el-card class="search-card">
        <div class="search-header">
          <h1 class="title">
            <el-icon><Avatar /></el-icon>
            黑神话悟空妖怪平生录
          </h1>
          <p class="subtitle">203个妖怪，203首小诗，203个妖生故事</p>
        </div>

        <div class="search-controls">
          <div class="search-input-wrapper">
            <el-autocomplete
              v-model="searchKeyword"
              :fetch-suggestions="handleSearchInput"
              placeholder="搜索妖怪名称..."
              class="search-input"
              @select="selectSuggestion"
              @keyup.enter="handleSearch"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-autocomplete>
          </div>

          <el-select
            v-model="selectedCategory"
            placeholder="选择类别"
            class="category-select"
            @change="handleCategoryChange"
            clearable
          >
            <el-option
              v-for="option in categoryOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>

          <el-button @click="handleClearFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>

        <!-- 统计信息 -->
        <div class="statistics">
          <el-tag
            v-for="(count, category) in yaoguaiStore.statistics.categoryStats"
            :key="category"
            :color="getCategoryColor(category)"
            effect="dark"
            class="stat-tag"
          >
            {{ category }}: {{ count }}
          </el-tag>
          <el-tag type="info" class="stat-tag">
            当前显示: {{ yaoguaiStore.yaoguaiList.length }}
          </el-tag>
        </div>
      </el-card>
    </div>

    <!-- 妖怪列表 -->
    <div class="content-section">
      <el-row :gutter="20" v-loading="yaoguaiStore.loading">
        <el-col
          v-for="yaoguai in yaoguaiStore.yaoguaiList"
          :key="yaoguai.id"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
          :xl="4"
          class="yaoguai-col"
        >
          <el-card
            class="yaoguai-card"
            :body-style="{ padding: '0px' }"
            shadow="hover"
            @click="viewYaoguaiDetail(yaoguai.id)"
          >
            <div class="card-image">
              <el-image
                :src="yaoguai.image"
                :alt="yaoguai.name"
                fit="cover"
                class="image"
                lazy
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                    <span>图片加载失败</span>
                  </div>
                </template>
              </el-image>
            </div>

            <div class="card-content">
              <h3 class="yaoguai-name">{{ yaoguai.name }}</h3>
              <div class="card-footer">
                <el-tag
                  :color="getCategoryColor(yaoguai.category)"
                  effect="dark"
                  size="small"
                >
                  {{ yaoguai.category }}
                </el-tag>
                <span class="yaoguai-id">{{ yaoguai.id }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 空状态 -->
      <el-empty
        v-if="yaoguaiStore.isEmpty"
        description="暂无妖怪数据"
        :image-size="200"
      >
        <el-button type="primary" @click="handleClearFilters">
          重新加载
        </el-button>
      </el-empty>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="yaoguaiStore.hasData">
        <el-pagination
          v-model:current-page="yaoguaiStore.pagination.current"
          v-model:page-size="yaoguaiStore.pagination.pageSize"
          :page-sizes="[12, 24, 48, 96]"
          :total="yaoguaiStore.pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
          background
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.search-section {
  margin-bottom: 20px;
}

.search-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 15px;
}

.search-header {
  text-align: center;
  margin-bottom: 20px;
}

.title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.subtitle {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin: 0;
}

.search-controls {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.search-input-wrapper {
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
}

.category-select {
  width: 150px;
}

.statistics {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.stat-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-tag:hover {
  transform: scale(1.05);
}

.content-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.yaoguai-col {
  margin-bottom: 20px;
}

.yaoguai-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  border-radius: 12px;
  overflow: hidden;
  height: 100%;
}

.yaoguai-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-image {
  height: 200px;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.yaoguai-card:hover .image {
  transform: scale(1.05);
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  background: #f5f7fa;
}

.card-content {
  padding: 15px;
}

.yaoguai-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 10px 0;
  text-align: center;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.yaoguai-id {
  font-size: 0.9rem;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

@media (max-width: 768px) {
  .home-container {
    padding: 10px;
  }

  .title {
    font-size: 2rem;
  }

  .search-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input-wrapper {
    min-width: auto;
  }

  .category-select {
    width: 100%;
  }
}
</style>
