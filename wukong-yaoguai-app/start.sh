#!/bin/bash

# 黑神话悟空妖怪平生录 - 启动脚本
echo "🚀 启动黑神话悟空妖怪平生录应用..."

# 检查 Node.js 版本
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查 nvm
if command -v nvm &> /dev/null; then
    echo "📦 使用 nvm 切换到 Node.js 22.16.0..."
    source ~/.nvm/nvm.sh
    nvm use 22.16.0
fi

# 检查依赖是否已安装
if [ ! -d "backend/node_modules" ]; then
    echo "📦 安装后端依赖..."
    cd backend && npm install && cd ..
fi

if [ ! -d "frontend/node_modules" ]; then
    echo "📦 安装前端依赖..."
    cd frontend && npm install && cd ..
fi

# 启动后端服务
echo "🔧 启动后端服务 (端口 3000)..."
cd backend
npm run dev &
BACKEND_PID=$!
cd ..

# 等待后端启动
echo "⏳ 等待后端服务启动..."
sleep 3

# 检查后端是否启动成功
if curl -s http://localhost:3000/api/health > /dev/null; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 启动前端服务
echo "🎨 启动前端服务 (端口 5173)..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo ""
echo "🎉 应用启动成功！"
echo ""
echo "📡 后端 API: http://localhost:3000"
echo "🌐 前端应用: http://localhost:5173"
echo ""
echo "📚 API 文档:"
echo "   GET  /api/yaoguai          - 获取妖怪列表"
echo "   GET  /api/yaoguai/:id      - 获取妖怪详情"
echo "   GET  /api/statistics       - 获取统计信息"
echo "   GET  /api/search/suggestions - 搜索建议"
echo "   GET  /api/health           - 健康检查"
echo ""
echo "💡 提示："
echo "   - 按 Ctrl+C 停止服务"
echo "   - 前端支持热重载，修改代码会自动刷新"
echo "   - 后端支持热重载，修改代码会自动重启"
echo ""

# 等待用户中断
wait
