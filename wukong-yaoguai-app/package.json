{"name": "wukong-yaoguai-app", "version": "1.0.0", "description": "黑神话悟空妖怪平生录 - 基于 Koa.js + Vue3 + Element Plus 的全栈应用", "main": "index.js", "scripts": {"start": "./start.sh", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "install:all": "npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["wukong", "yaoguai", "koa", "vue3", "element-plus", "fullstack", "typescript"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": ""}, "bugs": {"url": ""}, "homepage": ""}