<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>黑神话悟空妖怪平生录</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                <i class="bi bi-dragon me-2"></i>
                黑神话悟空妖怪平生录
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="bi bi-collection me-1"></i>
                            共收录 <span id="totalCount" class="fw-bold text-warning">0</span> 个妖怪
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container my-4">
        <!-- 搜索和筛选区域 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="searchInput" 
                                           placeholder="搜索妖怪名称...">
                                    <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                        <i class="bi bi-x-lg"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" id="categoryFilter">
                                    <option value="">全部类别</option>
                                    <option value="小妖">小妖</option>
                                    <option value="头目">头目</option>
                                    <option value="妖王">妖王</option>
                                    <option value="人物">人物</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 统计信息 -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex flex-wrap gap-3">
                                    <span class="badge bg-primary category-stat" data-category="小妖">
                                        小妖: <span id="count-小妖">0</span>
                                    </span>
                                    <span class="badge bg-success category-stat" data-category="头目">
                                        头目: <span id="count-头目">0</span>
                                    </span>
                                    <span class="badge bg-warning category-stat" data-category="妖王">
                                        妖王: <span id="count-妖王">0</span>
                                    </span>
                                    <span class="badge bg-info category-stat" data-category="人物">
                                        人物: <span id="count-人物">0</span>
                                    </span>
                                    <span class="badge bg-secondary">
                                        当前显示: <span id="currentCount">0</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 妖怪卡片网格 -->
        <div class="row" id="yaoguaiGrid">
            <!-- 妖怪卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 加载状态 -->
        <div class="text-center my-5" id="loadingSpinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载妖怪数据...</p>
        </div>

        <!-- 无结果提示 -->
        <div class="text-center my-5 d-none" id="noResults">
            <i class="bi bi-search display-1 text-muted"></i>
            <h4 class="text-muted mt-3">未找到相关妖怪</h4>
            <p class="text-muted">请尝试其他搜索条件</p>
        </div>

        <!-- 分页 -->
        <nav aria-label="妖怪列表分页" class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </ul>
        </nav>
    </div>

    <!-- 妖怪详情模态框 -->
    <div class="modal fade" id="yaoguaiModal" tabindex="-1" aria-labelledby="yaoguaiModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="yaoguaiModalLabel">妖怪详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- 详情内容将通过JavaScript动态生成 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" id="prevYaoguai">
                        <i class="bi bi-chevron-left"></i> 上一个
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="nextYaoguai">
                        下一个 <i class="bi bi-chevron-right"></i>
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <button type="button" class="btn btn-primary btn-floating" id="backToTop">
        <i class="bi bi-arrow-up"></i>
    </button>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JavaScript -->
    <script src="script.js"></script>
</body>
</html>
