# 黑神话悟空妖怪平生录 - 前端展示网页

203个妖怪，203首小诗，203个妖生故事，带你去看妖怪的众生相。

一个基于 jQuery 和 Bootstrap 5 的响应式网页，用于展示《黑神话悟空》游戏中的妖怪信息。

## 🎮 项目简介

本项目将《黑神话悟空妖怪平生录》的 Markdown 文档转换为交互式的网页展示，包含 203 个妖怪的详细信息，分为 4 个类别：小妖、头目、妖王、人物。

## 📋 开发需求（已完成）

1. ✅ **数据转换**：将 Markdown 文件转换为结构化的 JSON 数据
2. ✅ **前端展示**：开发妖怪生平录展示界面，支持查询和类别筛选
3. ✅ **技术选型**：使用 HTML/CSS/JavaScript + jQuery + Bootstrap 5
4. ✅ **AI 协助开发**：使用 Augment Agent 协助完成整个开发过程

## ✨ 功能特性

### 📱 响应式设计
- 支持桌面端、平板和移动设备
- 自适应网格布局（大屏幕 3-4 列，中屏幕 2 列，小屏幕 1 列）
- 优化的移动端交互体验

### 🔍 搜索和筛选
- **实时搜索**：按妖怪名称进行实时搜索
- **类别筛选**：支持按类别（小妖、头目、妖王、人物）筛选
- **组合筛选**：搜索和类别筛选可同时使用
- **统计信息**：实时显示各类别数量和当前显示数量

### 📄 分页功能
- 每页显示 12 个妖怪卡片
- 智能分页导航，支持页码跳转
- 分页状态保持，支持前进后退

### 🎯 详情展示
- **模态框展示**：点击卡片查看详细信息
- **完整信息**：包含妖怪图片、名称、类别、完整描述
- **键盘导航**：支持左右箭头键切换妖怪
- **按钮导航**：上一个/下一个妖怪按钮

### 🎨 视觉效果
- **Material Design** 风格设计
- **渐变背景**：现代化的视觉效果
- **动画效果**：卡片悬停、页面切换动画
- **图标支持**：Bootstrap Icons 图标库
- **中文字体**：Noto Serif SC 优化中文显示


