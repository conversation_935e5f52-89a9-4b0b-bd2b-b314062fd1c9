# 黑神话悟空妖怪平生录 - 前端展示网页

203个妖怪，203首小诗，203个妖生故事，带你去看妖怪的众生相。

一个基于 jQuery 和 Bootstrap 5 的响应式网页，用于展示《黑神话悟空》游戏中的妖怪信息。

## 🎮 项目简介

本项目将《黑神话悟空妖怪平生录》的 Markdown 文档转换为交互式的网页展示，包含 203 个妖怪的详细信息，分为 4 个类别：小妖、头目、妖王、人物。

## 📋 开发需求（已完成）

1. ✅ **数据转换**：将 Markdown 文件转换为结构化的 JSON 数据
2. ✅ **前端展示**：开发妖怪生平录展示界面，支持查询和类别筛选
3. ✅ **技术选型**：使用 HTML/CSS/JavaScript + jQuery + Bootstrap 5
4. ✅ **AI 协助开发**：使用 Augment Agent 协助完成整个开发过程

## ✨ 功能特性

### 📱 响应式设计
- 支持桌面端、平板和移动设备
- 自适应网格布局（大屏幕 3-4 列，中屏幕 2 列，小屏幕 1 列）
- 优化的移动端交互体验

### 🔍 搜索和筛选
- **实时搜索**：按妖怪名称进行实时搜索
- **类别筛选**：支持按类别（小妖、头目、妖王、人物）筛选
- **组合筛选**：搜索和类别筛选可同时使用
- **统计信息**：实时显示各类别数量和当前显示数量

### 📄 分页功能
- 每页显示 12 个妖怪卡片
- 智能分页导航，支持页码跳转
- 分页状态保持，支持前进后退

### 🎯 详情展示
- **模态框展示**：点击卡片查看详细信息
- **完整信息**：包含妖怪图片、名称、类别、完整描述
- **键盘导航**：支持左右箭头键切换妖怪
- **按钮导航**：上一个/下一个妖怪按钮

### 🎨 视觉效果
- **Material Design** 风格设计
- **渐变背景**：现代化的视觉效果
- **动画效果**：卡片悬停、页面切换动画
- **图标支持**：Bootstrap Icons 图标库
- **中文字体**：Noto Serif SC 优化中文显示

## 📁 文件结构

```
black-wukong-youji/
├── index.html              # 主页面
├── styles.css              # 样式文件
├── script.js               # JavaScript 逻辑
├── yaoguai-data.json       # 妖怪数据文件
├── parse-yaoguai.js        # 数据解析脚本
├── 黑神话悟空妖怪平生录.md   # 原始 Markdown 文件
└── README.md               # 项目说明
```

## 🚀 快速开始

### 1. 启动本地服务器

由于浏览器的同源策略，需要通过 HTTP 服务器访问：

**使用 Python（推荐）：**
```bash
# Python 3
python3 -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

**使用 Node.js：**
```bash
npx http-server -p 8000
```

### 2. 访问网页

打开浏览器访问：`http://localhost:8000`

## 🛠️ 技术栈

- **前端框架**：Bootstrap 5.3.2
- **JavaScript 库**：jQuery 3.7.1
- **图标库**：Bootstrap Icons 1.11.1
- **字体**：Google Fonts (Noto Serif SC)
- **数据格式**：JSON

## 📱 响应式断点

- **大屏幕** (≥1200px)：4 列布局
- **桌面** (≥992px)：3 列布局
- **平板** (≥768px)：2 列布局
- **手机** (<768px)：1 列布局

## 🎨 主题色彩

- **主色调**：渐变蓝紫色 (#667eea → #764ba2)
- **小妖**：蓝色系 (#3498db → #2980b9)
- **头目**：绿色系 (#27ae60 → #229954)
- **妖王**：橙色系 (#f39c12 → #e67e22)
- **人物**：紫色系 (#9b59b6 → #8e44ad)

## 🔧 开发过程记录

### 第一步：数据解析
使用 Augment Agent 创建了 `parse-yaoguai.js` 脚本：
- 解析 Markdown 文件中的妖怪信息
- 提取一级标题（妖怪种类）和二级标题（妖怪名称）
- 提取妖怪描述和图片路径
- 生成结构化的 JSON 数据文件

### 第二步：前端开发
创建响应式网页界面：
- `index.html`：主页面结构，使用 Bootstrap 5 组件
- `styles.css`：自定义样式，Material Design 风格
- `script.js`：JavaScript 逻辑，实现搜索、筛选、分页等功能

### 第三步：功能实现
- ✅ 妖怪卡片展示
- ✅ 实时搜索功能
- ✅ 类别筛选
- ✅ 分页导航
- ✅ 详情模态框
- ✅ 响应式设计
- ✅ 动画效果

## 📄 数据格式

`yaoguai-data.json` 文件结构：
```json
{
  "title": "黑神话悟空妖怪平生录",
  "description": "游戏中各种妖怪的详细介绍",
  "totalCount": 203,
  "categories": ["小妖", "头目", "妖王", "人物"],
  "yaoguai": [
    {
      "id": "1.1",
      "name": "狼斥候",
      "category": "小妖",
      "description": "妖怪的详细描述...",
      "image": "图片路径"
    }
  ]
}
```

## 🐛 故障排除

### 1. 数据加载失败
- 确保通过 HTTP 服务器访问（不是直接打开 HTML 文件）
- 检查 `yaoguai-data.json` 文件是否存在

### 2. 图片显示问题
- 图片路径相对于 HTML 文件位置
- 加载失败时会显示默认占位图

## 📊 项目统计

- **妖怪总数**：203 个
- **类别数量**：4 种（小妖、头目、妖王、人物）
- **代码文件**：4 个（HTML、CSS、JS、JSON）
- **开发时间**：约 2 小时（AI 协助）

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📜 许可证

本项目仅用于学习和展示目的。游戏相关内容版权归《黑神话悟空》开发商所有。
