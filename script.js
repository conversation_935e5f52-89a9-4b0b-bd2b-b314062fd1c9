$(document).ready(function() {
    // 全局变量
    let allYaoguai = [];
    let filteredYaoguai = [];
    let currentPage = 1;
    let itemsPerPage = 12;
    let currentYaoguaiIndex = 0;

    // 初始化应用
    initApp();

    function initApp() {
        loadYaoguaiData();
        bindEvents();
        initBackToTop();
    }

    // 加载妖怪数据
    function loadYaoguaiData() {
        $.getJSON('yaoguai-data.json')
            .done(function(data) {
                allYaoguai = data.yaoguai;
                filteredYaoguai = [...allYaoguai];
                
                // 更新统计信息
                updateStatistics(data);
                
                // 渲染妖怪列表
                renderYaoguaiList();
                
                // 隐藏加载动画
                $('#loadingSpinner').hide();
            })
            .fail(function() {
                $('#loadingSpinner').html(`
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        数据加载失败，请检查网络连接或刷新页面重试
                    </div>
                `);
            });
    }

    // 更新统计信息
    function updateStatistics(data) {
        $('#totalCount').text(data.totalCount);
        
        // 统计各类别数量
        const categoryCounts = {};
        data.categories.forEach(category => {
            categoryCounts[category] = data.yaoguai.filter(y => y.category === category).length;
            $(`#count-${category}`).text(categoryCounts[category]);
        });
    }

    // 渲染妖怪列表
    function renderYaoguaiList() {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const pageYaoguai = filteredYaoguai.slice(startIndex, endIndex);

        const $grid = $('#yaoguaiGrid');
        $grid.empty();

        if (pageYaoguai.length === 0) {
            $('#noResults').removeClass('d-none');
            $('#pagination').empty();
            $('#currentCount').text(0);
            return;
        }

        $('#noResults').addClass('d-none');
        $('#currentCount').text(filteredYaoguai.length);

        pageYaoguai.forEach((yaoguai, index) => {
            const card = createYaoguaiCard(yaoguai, startIndex + index);
            $grid.append(card);
        });

        // 添加动画效果
        $('.yaoguai-card').addClass('fade-in-up');

        // 渲染分页
        renderPagination();
    }

    // 创建妖怪卡片
    function createYaoguaiCard(yaoguai, index) {
        const categoryClass = `category-${yaoguai.category}`;
        
        return $(`
            <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4">
                <div class="card yaoguai-card h-100 shadow-sm" data-index="${index}">
                    <img src="${yaoguai.image}" class="card-img-top" alt="${yaoguai.name}" 
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+acquWcqDwvdGV4dD48L3N2Zz4='">
                    <div class="card-body">
                        <h6 class="card-title">${yaoguai.name}</h6>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge category-badge ${categoryClass}">${yaoguai.category}</span>
                            <small class="text-muted">${yaoguai.id}</small>
                        </div>
                    </div>
                </div>
            </div>
        `);
    }

    // 渲染分页
    function renderPagination() {
        const totalPages = Math.ceil(filteredYaoguai.length / itemsPerPage);
        const $pagination = $('#pagination');
        $pagination.empty();

        if (totalPages <= 1) return;

        // 上一页按钮
        const prevDisabled = currentPage === 1 ? 'disabled' : '';
        $pagination.append(`
            <li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" data-page="${currentPage - 1}">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `);

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            $pagination.append(`<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>`);
            if (startPage > 2) {
                $pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'active' : '';
            $pagination.append(`
                <li class="page-item ${activeClass}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                $pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
            }
            $pagination.append(`<li class="page-item"><a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a></li>`);
        }

        // 下一页按钮
        const nextDisabled = currentPage === totalPages ? 'disabled' : '';
        $pagination.append(`
            <li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" data-page="${currentPage + 1}">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `);
    }

    // 显示妖怪详情
    function showYaoguaiDetail(index) {
        const yaoguai = filteredYaoguai[index];
        currentYaoguaiIndex = index;

        const modalBody = `
            <div class="text-center mb-3">
                <img src="${yaoguai.image}" class="yaoguai-image" alt="${yaoguai.name}"
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjhmOWZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzZjNzU3ZCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+acquWcqDwvdGV4dD48L3N2Zz4='">
            </div>
            <div class="row mb-3">
                <div class="col-md-8">
                    <h4 class="mb-2">${yaoguai.name}</h4>
                </div>
                <div class="col-md-4 text-md-end">
                    <span class="badge category-badge category-${yaoguai.category} fs-6">${yaoguai.category}</span>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-12">
                    <small class="text-muted">编号: ${yaoguai.id}</small>
                </div>
            </div>
            <div class="yaoguai-description">${yaoguai.description}</div>
        `;

        $('#modalBody').html(modalBody);
        $('#yaoguaiModalLabel').text(yaoguai.name);
        
        // 更新导航按钮状态
        $('#prevYaoguai').prop('disabled', index === 0);
        $('#nextYaoguai').prop('disabled', index === filteredYaoguai.length - 1);
        
        $('#yaoguaiModal').modal('show');
    }

    // 搜索和筛选
    function filterYaoguai() {
        const searchTerm = $('#searchInput').val().toLowerCase().trim();
        const selectedCategory = $('#categoryFilter').val();

        filteredYaoguai = allYaoguai.filter(yaoguai => {
            const matchesSearch = !searchTerm || yaoguai.name.toLowerCase().includes(searchTerm);
            const matchesCategory = !selectedCategory || yaoguai.category === selectedCategory;
            return matchesSearch && matchesCategory;
        });

        currentPage = 1;
        renderYaoguaiList();
    }

    // 绑定事件
    function bindEvents() {
        // 搜索框事件
        $('#searchInput').on('input', debounce(filterYaoguai, 300));
        
        // 清除搜索
        $('#clearSearch').on('click', function() {
            $('#searchInput').val('');
            filterYaoguai();
        });

        // 类别筛选
        $('#categoryFilter').on('change', filterYaoguai);

        // 类别统计标签点击
        $('.category-stat').on('click', function() {
            const category = $(this).data('category');
            $('#categoryFilter').val(category);
            filterYaoguai();
        });

        // 妖怪卡片点击
        $(document).on('click', '.yaoguai-card', function() {
            const index = $(this).data('index');
            showYaoguaiDetail(index);
        });

        // 分页点击
        $(document).on('click', '.pagination .page-link', function(e) {
            e.preventDefault();
            const page = parseInt($(this).data('page'));
            if (page && page !== currentPage) {
                currentPage = page;
                renderYaoguaiList();
                $('html, body').animate({ scrollTop: 0 }, 500);
            }
        });

        // 模态框导航
        $('#prevYaoguai').on('click', function() {
            if (currentYaoguaiIndex > 0) {
                showYaoguaiDetail(currentYaoguaiIndex - 1);
            }
        });

        $('#nextYaoguai').on('click', function() {
            if (currentYaoguaiIndex < filteredYaoguai.length - 1) {
                showYaoguaiDetail(currentYaoguaiIndex + 1);
            }
        });

        // 键盘导航
        $(document).on('keydown', function(e) {
            if ($('#yaoguaiModal').hasClass('show')) {
                if (e.key === 'ArrowLeft' && currentYaoguaiIndex > 0) {
                    showYaoguaiDetail(currentYaoguaiIndex - 1);
                } else if (e.key === 'ArrowRight' && currentYaoguaiIndex < filteredYaoguai.length - 1) {
                    showYaoguaiDetail(currentYaoguaiIndex + 1);
                }
            }
        });
    }

    // 返回顶部功能
    function initBackToTop() {
        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $('#backToTop').fadeIn();
            } else {
                $('#backToTop').fadeOut();
            }
        });

        $('#backToTop').click(function() {
            $('html, body').animate({ scrollTop: 0 }, 600);
        });
    }

    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
});
