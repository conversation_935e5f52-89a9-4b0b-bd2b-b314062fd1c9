const fs = require('fs');
const path = require('path');

/**
 * 解析黑神话悟空妖怪平生录markdown文件，生成JSON数据
 */
function parseYaoguaiMarkdown() {
    try {
        // 读取markdown文件
        const markdownPath = path.join(__dirname, '黑神话悟空妖怪平生录.md');
        const content = fs.readFileSync(markdownPath, 'utf-8');
        
        // 分割内容为行
        const lines = content.split('\n');
        
        const yaoguaiData = [];
        let currentCategory = '';
        let currentYaoguai = null;
        let currentDescription = [];
        let isInDescription = false;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // 匹配一级标题（妖怪种类）
            if (line.match(/^# [一二三四五六七八九十]+、(.+)$/)) {
                const match = line.match(/^# [一二三四五六七八九十]+、(.+)$/);
                currentCategory = match[1];
                continue;
            }
            
            // 匹配二级标题（妖怪名称）
            if (line.match(/^## \d+\.\d+ (.+)$/)) {
                // 保存上一个妖怪的数据
                if (currentYaoguai) {
                    currentYaoguai.description = currentDescription.join('\n').trim();
                    yaoguaiData.push(currentYaoguai);
                }
                
                // 开始新的妖怪
                const match = line.match(/^## (\d+\.\d+) (.+)$/);
                currentYaoguai = {
                    id: match[1],
                    name: match[2],
                    category: currentCategory,
                    description: '',
                    image: ''
                };
                currentDescription = [];
                isInDescription = false;
                continue;
            }
            
            // 跳过空行
            if (line === '') {
                if (isInDescription) {
                    currentDescription.push('');
                }
                continue;
            }
            
            // 匹配图片
            if (line.match(/^!\[.*\]\(.+\)$/)) {
                if (currentYaoguai) {
                    const imageMatch = line.match(/^!\[.*\]\((.+)\)$/);
                    currentYaoguai.image = imageMatch[1];
                }
                continue;
            }
            
            // 如果当前有妖怪对象，且不是标题行，则作为描述内容
            if (currentYaoguai && !line.startsWith('#')) {
                isInDescription = true;
                currentDescription.push(line);
            }
        }
        
        // 保存最后一个妖怪的数据
        if (currentYaoguai) {
            currentYaoguai.description = currentDescription.join('\n').trim();
            yaoguaiData.push(currentYaoguai);
        }
        
        // 生成JSON文件
        const jsonData = {
            title: "黑神话悟空妖怪平生录",
            description: "黑神话悟空游戏中各种妖怪的详细介绍和背景故事",
            totalCount: yaoguaiData.length,
            categories: [...new Set(yaoguaiData.map(y => y.category))],
            yaoguai: yaoguaiData
        };
        
        // 写入JSON文件
        const outputPath = path.join(__dirname, 'yaoguai-data.json');
        fs.writeFileSync(outputPath, JSON.stringify(jsonData, null, 2), 'utf-8');
        
        console.log(`✅ 解析完成！`);
        console.log(`📊 总计解析妖怪: ${yaoguaiData.length} 个`);
        console.log(`📂 妖怪种类: ${jsonData.categories.length} 种`);
        console.log(`📁 输出文件: ${outputPath}`);
        
        // 显示统计信息
        const categoryStats = {};
        yaoguaiData.forEach(y => {
            categoryStats[y.category] = (categoryStats[y.category] || 0) + 1;
        });
        
        console.log('\n📈 各类妖怪数量统计:');
        Object.entries(categoryStats).forEach(([category, count]) => {
            console.log(`  ${category}: ${count} 个`);
        });
        
        return jsonData;
        
    } catch (error) {
        console.error('❌ 解析失败:', error.message);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    parseYaoguaiMarkdown();
}

module.exports = { parseYaoguaiMarkdown };
